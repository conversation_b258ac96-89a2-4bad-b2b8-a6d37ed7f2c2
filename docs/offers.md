# Offers Management System

## Table of Contents
- [Overview](#overview)
- [Architecture Overview](#architecture-overview)
- [Data Models and Schema](#data-models-and-schema)
- [User Interface and Interaction Patterns](#user-interface-and-interaction-patterns)
- [User Experience Flow](#user-experience-flow)
- [Data Flow Architecture](#data-flow-architecture)
- [API Endpoints and Services](#api-endpoints-and-services)
- [Business Logic and State Management](#business-logic-and-state-management)
- [Integration Architecture](#integration-architecture)
- [Security and Validation](#security-and-validation)
- [Performance Optimization](#performance-optimization)
- [Error Handling](#error-handling)
- [Implementation Patterns](#implementation-patterns)

## Overview

The Offers Management System is a comprehensive promotional framework within the Perkd application that enables merchants to create, distribute, and manage digital promotional campaigns. The system supports multiple offer types (discounts, vouchers, tickets) with sophisticated lifecycle management, real-time redemption tracking, and secure sharing capabilities. Built on a multi-layered architecture, it provides seamless integration with card management, payment processing, and user engagement systems.

### Key Capabilities

- **Multi-Type Offer Support**: Discounts, vouchers, and event tickets with specialized behaviors
- **Lifecycle Management**: Complete offer lifecycle from creation to expiration with state transitions
- **Real-Time Redemption**: Secure redemption processing with multiple authentication methods
- **Sharing and Transfer**: User-to-user offer sharing with policy enforcement
- **Contextual Discovery**: Location and behavior-based offer recommendations
- **Cross-Platform Redemption**: Support for in-store, online, and hybrid redemption channels

### Technology Foundation

The offers system is built on the same robust technology stack as the core Perkd application:
- **Data Persistence**: Realm database with schema version 278+ for local storage
- **Synchronization**: Bidirectional sync with backend APIs for real-time updates
- **Event System**: Centralized event-driven architecture for loose coupling
- **State Management**: Sophisticated state machines for offer lifecycle management
- **Security**: Multi-layered security with encryption and validation

## Architecture Overview

The Offers Management System follows a sophisticated multi-layered architecture that emphasizes modularity, scalability, and maintainability while providing rich user experiences and robust data management.

### System Architecture

```mermaid
graph TB
    subgraph "🎯 User Experience Layer"
        UX1[Offer Discovery<br/>Location & Context-based]
        UX2[Offer Details<br/>Rich Media & Terms]
        UX3[Redemption Flow<br/>Multi-channel Support]
        UX4[Sharing Interface<br/>Social Distribution]
    end

    subgraph "📱 Presentation Layer"
        P1[Offer Containers<br/>src/containers/Offer/]
        P2[Offer Components<br/>src/components/Offer/]
        P3[Offer Widgets<br/>Card Integration]
        P4[Navigation<br/>Modal & Stack]
    end

    subgraph "🎮 Business Logic Layer"
        B1[Offer Controller<br/>src/controllers/Offer.js]
        B2[Offer Actions<br/>src/lib/common/actions/offer.js]
        B3[Offer Services<br/>src/lib/common/services/offer.js]
        B4[State Management<br/>src/lib/offers.js]
    end

    subgraph "💾 Data Layer"
        D1[Offer Model<br/>src/lib/models/Offer.js]
        D2[Related Models<br/>Code, Discount, Redemption]
        D3[Realm Database<br/>Local Storage]
        D4[Sync Engine<br/>Bidirectional Sync]
    end

    subgraph "🌐 External Integration"
        E1[Backend APIs<br/>REST Endpoints]
        E2[Card Services<br/>Integration]
        E3[Payment Systems<br/>Transaction Processing]
        E4[Location Services<br/>Geofencing]
    end

    %% User Experience Flow
    UX1 --> UX2
    UX2 --> UX3
    UX2 --> UX4
    UX3 --> UX1

    %% Presentation Layer Connections
    UX1 --> P1
    UX2 --> P1
    UX3 --> P1
    UX4 --> P1
    P1 --> P2
    P1 --> P3
    P1 --> P4

    %% Business Logic Connections
    P1 --> B1
    P2 --> B1
    B1 --> B2
    B1 --> B3
    B2 --> B4
    B3 --> B4

    %% Data Layer Connections
    B2 --> D1
    B3 --> D1
    D1 --> D2
    D1 --> D3
    D3 --> D4

    %% External Integration
    B3 --> E1
    B1 --> E2
    B3 --> E3
    B1 --> E4
    D4 --> E1

    %% Styling with darker backgrounds and white text
    classDef ux fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef presentation fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef business fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef data fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef external fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class UX1,UX2,UX3,UX4 ux
    class P1,P2,P3,P4 presentation
    class B1,B2,B3,B4 business
    class D1,D2,D3,D4 data
    class E1,E2,E3,E4 external
```

### Architectural Principles

- **Separation of Concerns**: Clear boundaries between presentation, business logic, and data layers
- **Event-Driven Communication**: Loose coupling through centralized event system
- **State Management**: Centralized offer state management with real-time updates
- **Service Integration**: Modular integration with card, payment, and location services
- **Performance Optimization**: Multi-level caching and lazy loading strategies

### Widget-Based Architecture

The offers system leverages the Perkd widget framework to provide modular, reusable components that integrate seamlessly with the card management system:

#### Offer Widget Implementation

The Offer Widget serves as the primary entry point for offer functionality within cards, extending the DataWidget framework:

````javascript
export default class Offer extends DataWidget {
    constructor(definition, owner, data = {}, credentials) {
        super(definition, owner, data, credentials);

        // Automatic kind filtering to prevent conflicts
        const exclude = this.master.widgets
            .filter(w => w.param.model === OFFER)
            .reduce((kinds, w) => kinds.concat(w.param.kinds), []);

        if (!param.kinds && !!exclude.length) {
            const kinds = _.Offer.kinds(cardId).filter(k => !exclude.includes(k));
            Object.assign(param, { kinds: kinds.length ? kinds : [OFFER] });
        }
    }

    init() {
        super.init();
        this.Controller = Controller;
    }
}
````

#### Widget Configuration and Lifecycle

**Configuration Parameters:**
- **Model**: Specifies the data model (default: 'offer')
- **Kinds**: Defines which offer types to display (e.g., 'offer', 'ticket', 'voucher')
- **Filtering**: Automatically excludes kinds handled by other widgets on the same card

**Lifecycle Management:**
- **Constructor**: Sets up initial configuration and determines offer kinds to display
- **init()**: Initializes the widget and sets the Controller reference
- **addListeners()**: Registers for offer update and deletion events
- **removeListeners()**: Cleans up event listeners
- **onDone()**: Handles cleanup when the widget is closed, clearing view state

#### Widget Integration Patterns

- **Base Class**: Extends `DataWidget` to leverage data management capabilities
- **Child Classes**: Specialized widgets like `Ticket` extend the base Offer Widget
- **Controller Integration**: Initializes and communicates with the Offer Controller
- **Event Management**: Manages lifecycle events and updates through the Event system

## Data Models and Schema

The Offers Management System employs a sophisticated data model architecture with multiple interconnected schemas that support complex offer types, redemption patterns, and business rules.

### Core Offer Schema

The primary `Offer` model serves as the foundation for all offer types with comprehensive properties for different use cases:

**Key Properties:**
- **Identity**: `id` (primary key), `masterId` (template reference), `cardId` (association)
- **Content**: `name`, `title`, `description`, `terms`, `brand`
- **Classification**: `kind` (discount/voucher/ticket), `state`, `merchantId`
- **Timing**: `startTime`, `endTime`, `issuedAt`, `modifiedAt`
- **Redemption**: `code`, `barcode`, `barcodeType`, `redemption` object
- **Media**: `images` array, `style` configuration
- **Behavior**: `options`, `place`, `venue`, `checkin` details

### Offer Types and Processing Patterns

The system supports three primary offer types with fundamentally different processing patterns:

#### 1. Discount Offers (Standard Redemption Processing)
- **Purpose**: Standard promotional offers providing price reductions
- **Data Focus**: Discount value, percentage/fixed amount, product qualifiers
- **Processing**: Through standard offer redemption system with authentication methods
- **Preferred Authentication**: Manual code display, QR code scan, NFC touch, barcode display
- **Channel Compatibility**: All channels (in-store, online, mobile app, partner systems)
- **Special Features**: Auto-apply during checkout, stacking rules, item-level validation, multiple redemptions
- **Use Cases**: Store-wide promotions, product-specific discounts, loyalty rewards

#### 2. Vouchers (Payment Method Processing)
- **Purpose**: Value-based payment instruments processed through payment system
- **Data Focus**: Fixed monetary value, payment method integration (`options.payment: true`)
- **Processing**: Through payment system as selectable payment methods, NOT redemption system
- **Authentication**: Payment method selection during checkout (no QR/NFC/manual code redemption)
- **Channel Compatibility**: Checkout payment flow only
- **Special Features**: Single-use payment instruments, partial payment support, excluded from discount flow
- **Use Cases**: Gift certificates, prepaid vouchers, promotional payment credits
- **Technical Identification**: `kind === "voucher" && options?.payment === true && discount?.kind === "FIXED"`

#### 3. Tickets (Event/Service Processing)
- **Purpose**: Time-bound offers for events or services with check-in functionality
- **Data Focus**: Event details, venue information, check-in windows, location constraints
- **Processing**: Through standard redemption system with specialized check-in features
- **Preferred Authentication**: NFC touch and QR code scanning for venue verification
- **Channel Compatibility**: In-store (venue-specific), mobile app, machine/vending integration
- **Special Features**: Auto check-in detection, venue proximity validation, time window enforcement
- **Use Cases**: Event tickets, service appointments, time-limited access, venue check-ins

#### Processing Pattern Compatibility Matrix

| Offer Type | Redemption System | Payment System | Authentication Methods | Checkout Integration |
|------------|-------------------|----------------|------------------------|---------------------|
| **Discount** | ✅ Primary | ❌ No | QR, NFC, Manual Code, Barcode | Auto-apply discounts |
| **Voucher** | ❌ Excluded | ✅ Primary | Payment method selection | Payment amount reduction |
| **Ticket** | ✅ Primary | ❌ No | QR, NFC (venue-specific) | Check-in validation |

#### Channel Compatibility for Redemption Offers Only

| Offer Type | In-Store | Online | Mobile App | Partner Systems | Machine/Vending |
|------------|----------|--------|------------|-----------------|-----------------|
| **Discount** | ✅ Full | ✅ Full | ✅ Full | ✅ Full | ✅ Full |
| **Ticket** | ✅ Full | ❌ N/A | ✅ Full | ✅ Full | ✅ Full |

*Note: Vouchers are not included in redemption channel compatibility as they operate exclusively through the payment system during checkout.*

### Voucher Payment System Integration

Vouchers with `options.payment: true` are processed as payment methods rather than standard redemption offers, representing a hybrid implementation pattern:

#### Voucher Payment Processing Flow

1. **Identification**: System identifies offers with `kind === "voucher"` and `options.payment === true`
2. **Collection**: Vouchers are collected as available payment methods during checkout
3. **Presentation**: Vouchers are presented to users in the form of an Offer when issued, and appear  in payment method selection UI alongside other payment options
4. **Selection**: User selects voucher as payment method (not through offer redemption)
5. **Processing**: Voucher processes through payment system with `type: CASH` and `offerId` reference
6. **Value Application**: Voucher value applies as payment amount, not as discount

#### Exclusion from Redemption Flow

Vouchers are explicitly excluded from the standard redemption and discount application flow:

```javascript
// Exclusion from discount application
function qualifiedOffers(offers, policyOffers, removedDiscounts, excludeOffers) {
    return offers.filter(o => {
        // Other qualification criteria...
        && !isCashVoucher(o)  // Vouchers excluded from discount flow
        // Other qualification criteria...
    });
}
```

This hybrid implementation allows voucher offers to be managed through the offer system but processed through the payment system, providing a unified offer management experience while maintaining separation of processing concerns.

### Offer Type State Mapping

Each offer type follows a consistent state model with customized frontend status mapping for optimal user experience:

| Backend State | Discount/Voucher Status | Ticket Status |
|---------------|-------------------------|---------------|
| ACTIVE | ACTIVE | TOCHECKIN/INPROGRESS |
| REDEEMED | REDEEMED | CHECKEDIN/USED |
| UPCOMING | UPCOMING | UPCOMING |
| EXPIRED | EXPIRED | EXPIRED |
| TRANSFERRED | TRANSFERRED | TRANSFERRED |
| TRANSFERRING | TRANSFERRING | TRANSFERRING |
| FULLY_REDEEMED | FULLY_REDEEMED | FULLY_REDEEMED |
| CANCELLED | CANCELLED | CANCELLED |

This mapping allows the same backend state system to provide contextually appropriate user experiences for different offer types while maintaining data consistency.

### Related Data Models

The offer system includes several specialized models that extend core functionality:

- **OfferCode**: Manages redemption codes for different channels (store, online, in-store)
- **OfferDiscount**: Handles discount calculations, qualifiers, and entitlements
- **Redemption**: Defines redemption rules, limits, channels, and authorization methods
- **OfferImage**: Manages visual assets and presentation media
- **OfferWhen**: Tracks timing events (received, viewed, redeemed, expired)
- **OfferTouchPoint**: Defines redemption interaction points and methods

### State Model and Transitions

The offer state system provides comprehensive lifecycle management with sophisticated state transitions:

```mermaid
stateDiagram-v2
    [*] --> PENDING : Offer Created
    PENDING --> ACTIVE : Activation Time
    PENDING --> CANCELLED : Merchant Cancel
    
    ACTIVE --> REDEEMED : User Redeems
    ACTIVE --> UPCOMING : Future Start
    ACTIVE --> EXPIRED : End Time Passed
    ACTIVE --> TRANSFERRING : Share Initiated
    
    UPCOMING --> ACTIVE : Start Time Reached
    UPCOMING --> EXPIRED : End Time Passed
    UPCOMING --> CANCELLED : Merchant Cancel
    
    REDEEMED --> FULLY_REDEEMED : All Uses Exhausted
    REDEEMED --> EXPIRED : End Time Passed
    
    TRANSFERRING --> TRANSFERRED : Share Complete
    TRANSFERRING --> ACTIVE : Share Cancelled
    
    TRANSFERRED --> [*] : Final State
    FULLY_REDEEMED --> [*] : Final State
    EXPIRED --> [*] : Final State
    CANCELLED --> [*] : Final State
```

| State | Description | User Actions | System Behavior |
|-------|-------------|--------------|-----------------|
| `PENDING` | Created but not active | None | Hidden from user |
| `ACTIVE` | Available for redemption | Redeem, Share, View | Full functionality |
| `UPCOMING` | Scheduled for future | View, Share | Preview mode |
| `REDEEMED` | Successfully used | View History | Track usage |
| `FULLY_REDEEMED` | All uses exhausted | View History | Archive state |
| `EXPIRED` | Past expiration | View History | Read-only |
| `TRANSFERRED` | Shared to other user | View History | Track provenance |
| `TRANSFERRING` | Share in progress | Cancel Transfer | Temporary state |
| `CANCELLED` | Merchant cancelled | None | Hidden from user |

### Data Relationships

The offer data model maintains complex relationships with other system entities to support comprehensive functionality:

```mermaid
erDiagram
    Offer ||--|| OfferCode : has
    Offer ||--o| OfferDiscount : contains
    Offer ||--|| Redemption : defines
    Offer ||--o{ OfferImage : displays
    Offer ||--|| OfferWhen : schedules
    Offer }|--|| Card : belongs_to
    Offer }|--|| CardMaster : issued_by
    Redemption ||--o| OfferTouchPoint : through

    Offer {
        string id PK
        string masterId FK
        string cardId FK
        string kind
        string state
        datetime startTime
        datetime endTime
    }

    OfferCode {
        string id PK
        string store
        string instore
        string online
    }

    Redemption {
        string id PK
        int limit
        int remain
        bool multiple
        string authorize
    }

    %% Styling with darker backgrounds and white text
    %%{init: {'theme':'dark'}}%%
```

**Key Relationships:**
- **Card Association**: Each offer belongs to a specific loyalty card and inherits card-level permissions
- **Master Template**: Offers are instantiated from CardMaster templates with business rules
- **Code Management**: Redemption codes support multiple channels (in-store, online, mobile)
- **Media Assets**: Rich media support with multiple image formats and sizes
- **Timing Control**: Sophisticated scheduling with start/end times and event tracking

## User Interface and Interaction Patterns

This section summarizes the key UI flows and interaction patterns in the Perkd offers system, focusing on how users discover, view, and redeem offers.

### Offer Discovery

Users find offers through:

#### Card-Associated Discovery
- **Widget Integration**: Offers appear as widgets within loyalty card views with automatic filtering by type
- **Real-Time Updates**: Offer availability updates dynamically as users navigate between cards

#### Location-Based Discovery
- **Contextual Notifications**: Push notifications trigger when entering geofenced areas with available offers
- **Proximity-Based Sorting**: Offers sorted by distance and relevance when location services enabled

### Offer List and Detail View

#### Sectioned List Display
- **Status-Based Grouping**: Automatic grouping by status (Available, Upcoming, Redeemed, Expired)
- **Priority Sorting**: Sorted by priority, expiration date, and relevance within sections
- **Dynamic Updates**: Real-time list updates as offer states change

#### List Item Interactions
- **Tap to View Details**: Single tap opens detailed offer view
- **Visual Indicators**: Color-coded status badges, unread badges, and total count badges

#### Detail Screen Features
- **Full-Screen Display**: Immersive presentation with rich media support
- **Image Gallery**: Swipeable gallery for multiple promotional images
- **Expandable Sections**: Collapsible terms, conditions, and redemption instructions
- **Real-Time Updates**: Live status updates and countdown timers for time-limited offers

### Redemption Interface Patterns

#### Channel Selection & Redemption

The offer redemption system provides different interfaces based on the selected redemption channel:

**Channel Selection (Pre-Redemption):**
When an offer supports multiple redemption channels (e.g., In-App, In-Store, Online), users are presented with clear button-based options to choose their preferred method. However, if an offer only supports In-Store/Online redemption, the channel selection step is skipped, and the slide-to-redeem button is shown directly.
- **"In App" Button** (PERKD Channel):
  - **Action:** Tapping this button navigates directly to the Shop Widget for in-app redemption.
- **"In Store & Online" Button** (STORE/ONLINE Channels):
  - **Action:** Tapping this button initiates a UI state change on the same screen:
    - The "In Store & Online" button transforms in place into the "slide-to-redeem" component.
    - Concurrently, the "In App" button reduces in size and changes to a smaller Shop icon only. This allows the user to still access or switch back to the in-app redemption flow if desired.
- **UI Implementation (Dynamic Buttons):**
  - **Auto-Applied Interface**: For PERKD-only offers with shop widget support
  - **No Buttons**: For PERKD-only offers without shop widget support
  - **Single Button**: For STORE/ONLINE-only offers (slide-to-redeem interface)
  - **Two Buttons**: For multi-channel offers with PERKD + (STORE/ONLINE) channels
- **Channel Grouping**: STORE and ONLINE channels are always grouped together as "In Store & Online" - they never appear as separate buttons
- **PERKD Separation**: PERKD (in-app) channel always appears as a separate "Shop" button when present with other channels

**PERKD Channel Shop Widget Interface:**

The PERKD channel provides a completely different redemption experience that bypasses traditional authentication methods:

**Implementation Details:**
- **PERKD-Only Offers**: Display auto-applied interface with "offer.autoapplied" text and shopping bag icon
- **Multi-Channel Offers**: Provide "In App" button that launches shop widget when selected
- **Shop Widget Navigation**: Uses `onPressToShop()` method to handle URL schemes or web navigation
- **No User Authentication Required**: Bypasses traditional authentication methods (QR/NFC/manual codes) but still performs discount qualification validation during checkout
- **Configuration-Driven**: Behavior determined by `options.buttonLink.app` (URL scheme) or `options.buttonLink.web` (web URL)

**User Experience Flow:**
1. **PERKD-Only**: User sees auto-applied message → Taps to navigate to shop → Discount automatically applied
2. **Multi-Channel**: User selects "In App" button → Navigates to shop widget → Discount automatically applied

**Slide-to-Redeem Interface (Store/Online Channels Only):**

The slide-to-redeem gesture interface is used specifically for in-store and online redemption channels that require authentication methods:

**Core Mechanics:**
- **Gesture Handling**: PanResponder-based slide animation with haptic feedback and progress indication
- **Threshold Detection**: Automatic completion detection with reset mechanism for incomplete gestures
- **Visual Feedback**: Dynamic text overlay, opacity animation, scale effects, and completion feedback
- **Lock Prevention**: Touch locking during processing to prevent double-redemption
- **Remaining Count Display**: Shows remaining redemption count for multi-use offers (e.g., "remaining 3 times") on the slide button text when `remain > 1`

**Authentication Flow (Post-Slide):**
- **Configuration-Driven**: Authentication method automatically determined by offer's `authorize` property
- **QR Scanning**: When `authorize === 'scan'` - launches camera for QR code scanning
- **NFC Touch**: When `authorize === 'nfc'` - activates NFC reader for tap authentication
- **Code Display**: When no `authorize` property - shows barcode/manual code interface

#### Complete Redemption Walkthrough

The offer redemption flow provides a comprehensive step-by-step user experience:

```mermaid
graph TD
    A[Offer Discovery] --> B[Detail Review]
    B --> C{Channel Configuration}

    C -->|PERKD Only + Shop Widget| D[Auto-Applied Shop Interface]
    C -->|PERKD Only + No Shop Widget| E[No Redemption Button]
    C -->|Multi-Channel| F{User Channel Selection}
    C -->|STORE/ONLINE Only| G[Slide-to-Redeem]

    F -->|"In App" Button| H[Shop Widget Navigation]
    F -->|"In Store & Online" Button| I[Button State Change]

    I --> G
    G --> J{Authentication Method}
    J -->|authorize === 'scan'| K[QR Scanner Interface]
    J -->|authorize === 'nfc'| L[NFC Reader Interface]
    J -->|No authorize property| M[Code Display Interface]

    D --> N[Shop Widget Integration]
    H --> N

    K --> O[Real-Time Validation]
    L --> O
    M --> O

    O --> P{Validation Result}
    P -->|Success| Q[Success Confirmation]
    P -->|Error| R[Error Handling]
    R --> S[Recovery Options]
    S --> J
    Q --> T[Redemption Complete]

    N --> U[Shop Widget]

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style F fill:#fff3e0
    style Q fill:#e8f5e8
    style R fill:#ffebee
    style T fill:#e8f5e8
    style U fill:#e8f5e8
```

**Step-by-Step Process:**
1. **Offer Selection**: User taps on desired offer from sectioned list with visual scanning
2. **Detail Review**: Progressive image loading, content review, and action decision
3. **Channel Configuration Check**: System determines interface based on offer's `redemption.channels` array:
   - **PERKD-only + Shop Widget**: Shows auto-applied interface with direct shop navigation
   - **PERKD-only + No Shop Widget**: Shows no redemption button (offer not redeemable in app)
   - **Multi-channel (PERKD + STORE/ONLINE)**: Shows dual button interface for user selection
   - **STORE/ONLINE-only**: Shows slide-to-redeem interface directly
4. **Interface Presentation**: System presents appropriate redemption interface:
   - **Shop Widget Interface**: For PERKD channel - direct navigation to integrated shopping experience
   - **Slide-to-Redeem Interface**: For STORE/ONLINE channels - authentication-based redemption
5. **Redemption Execution**:
   - **Shop Widget Flow**: Direct navigation → Shop integration → Auto-applied discounts
   - **Slide-to-Redeem Flow**: Gesture with haptic feedback → Authentication method determination → Authentication execution
6. **Authentication Method**: For STORE/ONLINE channels only - system automatically determines method based on offer configuration (`authorize` property)
7. **Completion**: Success confirmation with state updates and digital receipt generation

### Authentication Method

The app provides multiple authentication methods for offer redemption:

#### QR Code Scanning
- **Camera Interface**: Native camera with QR detection overlay and alignment guides
- **Auto-Detection**: Automatic scanning with visual/audio feedback and fallback to manual entry

#### NFC Touch Interaction
- **Initialization**: System checks NFC availability and provides positioning instructions
- **Data Exchange**: Secure exchange with real-time proximity feedback and error recovery

#### Manual Code Entry
- **Code Display**: Large, formatted redemption code with one-tap copy functionality
- **External Validation**: User completes validation on merchant system outside app

#### Barcode Display
- **Generation**: High-quality barcode with automatic brightness optimization
- **Merchant Scanning**: POS system scanning with real-time validation feedback

#### Authentication Method Selection Flow (STORE/ONLINE Channels Only)

**Note:** This flow applies only to STORE/ONLINE redemption channels. PERKD channel uses shop widget navigation instead.

```mermaid
graph TD
    A[STORE/ONLINE Redemption Initiated] --> B{Offer Configuration Check}
    B -->|authorize === 'scan'| C[QR Code Scanning]
    B -->|authorize === 'nfc'| D[NFC Touch]
    B -->|No authorize property| E[Code Display Interface]

    C --> F{Camera Available?}
    F -->|Yes| G[Launch Scanner]
    F -->|No| H[Permission Request]
    H --> I{Permission Granted?}
    I -->|Yes| G
    I -->|No| J[Error Message]

    D --> K{NFC Available?}
    K -->|Yes| L[NFC Reader Active]
    K -->|No| M[Error Message]

    G --> N[Scan Success]
    L --> O[NFC Read Success]

    N --> P[Content Validation]
    O --> P
    P --> Q[Redemption Complete]

    style A fill:#e1f5fe
    style P fill:#e8f5e8
    style Q fill:#e8f5e8
    style J fill:#ffebee
    style M fill:#ffebee
```

### Sharing and Social Features

#### Share Methods
- **Contact Integration**: Native contact picker for direct sharing
- **Social Media**: Integration with social platforms and direct messaging (SMS/email)
- **Link Generation**: Shareable links with expiration tracking and QR codes for in-person sharing

#### Sharing Management
- **Recipient Validation**: Real-time eligibility checking and policy enforcement
- **Message Customization**: Editable templates with merchant branding
- **Batch Operations**: Multi-recipient sharing with preview functionality
- **Transfer Controls**: Confirmation dialogs, cancellation options, and complete sharing history

### Navigation and State Management

#### Navigation Patterns
- **Cross-Navigation**: Direct navigation between cards and offers with deep link support
- **Modal Presentations**: Redemption confirmations and sharing interfaces as modals
- **State Preservation**: Navigation state maintained across app lifecycle events

#### Offer State Management
- **Status Indicators**: Visual indicators for redemption readiness and channel availability
- **Usage Tracking**: Real-time display of remaining redemptions and expiration warnings
- **Historical Access**: Expired offers accessible in dedicated sections with countdown timers for upcoming offers

#### Visual Feedback
- **Animations**: Smooth slide-to-redeem progress, state transitions, and completion effects
- **Haptic Feedback**: Tactile feedback at gesture milestones and completion
- **Loading States**: Skeleton animations and error recovery with success confirmations

#### Accessibility Features
- **Screen Reader**: Semantic labels and logical reading order
- **Visual Support**: High contrast mode, dynamic type sizing, and color-blind friendly design
- **Motor Support**: 44pt touch targets, gesture alternatives, and voice control compatibility

## User Experience Flow

The Offers Management System provides a comprehensive user experience that spans discovery, engagement, redemption, and sharing across multiple touchpoints and interaction patterns.

### High-Level User Journey Overview

The offers system provides an end-to-end user experience from discovery through redemption. For detailed information about specific app features and UI interaction patterns, see the [User Interface and Interaction Patterns](#user-interface-and-interaction-patterns) section.

```mermaid
graph TB
    A[User Opens App] --> B{Discovery Context}
    B -->|Card View| C[Card-Associated Offers]
    B -->|Location| D[Proximity-Based Offers]
    B -->|Browse| E[Catalog Discovery]
    B -->|Notification| F[Targeted Campaigns]

    C --> G[Offer Selection]
    D --> G
    E --> G
    F --> G

    G --> H[Offer Detail Review]
    H --> I{User Decision}
    I -->|Redeem| J[Authentication Flow]
    I -->|Share| K[Social Distribution]
    I -->|Save| L[Future Use]

    J --> M[Redemption Complete]
    K --> N[Share Tracking]
    L --> O[Reminder System]

    style A fill:#e1f5fe
    style M fill:#e8f5e8
    style N fill:#fff3e0
    style O fill:#f3e5f5
```

### Core User Journey Patterns

The offers system supports three primary user journey patterns. For detailed interface interactions and app features, see [User Interface and Interaction Patterns](#user-interface-and-interaction-patterns).

#### 1. Discovery-to-Redemption Journey
Users discover offers through contextual triggers, review details, and complete redemption using various authentication methods.

#### 2. Social Sharing Journey
Users discover valuable offers, share them with their network, and track engagement and redemption by recipients.

#### 3. Loyalty Integration Journey
Users access offers through loyalty card interfaces, leveraging existing merchant relationships for enhanced engagement.

### Authentication Methods and System Boundaries

The redemption system implements multiple authentication methods with clearly defined system boundaries between Perkd application responsibilities and external merchant system responsibilities:

#### QR Code Scan Authentication
- **Perkd Responsibility**: QR code generation, scanning interface, content decoding
- **Implementation**: Uses standard QR_CODE_SCANNER configuration with content validation
- **Data Flow**: QR code contains redemption data → Perkd scans and validates → Redemption processed
- **Use Cases**: In-store redemption, partner system integration, machine/vending systems

#### NFC Touch Authentication
- **Perkd Responsibility**: NFC protocol implementation, device communication, data parsing
- **Implementation**: NFC initialization, secure element validation, JSON content parsing
- **Data Flow**: NFC device presents data → Perkd reads and validates → Redemption processed
- **Use Cases**: Contactless in-store redemption, ticket check-in, venue validation

#### Barcode Display Authentication
- **Perkd Responsibility**: Barcode generation, display formatting, visual presentation
- **Merchant Responsibility**: Barcode scanning, validation, transaction processing
- **Implementation**: Perkd displays barcode → Merchant scans → External validation
- **Use Cases**: Traditional POS integration, merchant-controlled validation

#### Manual Code Entry - External Validation Design
- **Perkd Responsibility**: Code generation, secure display, copy-to-clipboard functionality
- **Merchant Responsibility**: Code entry interface, validation logic, transaction processing
- **System Boundary**: Perkd provides codes; merchants validate externally
- **Implementation Details**:
  ```
  Perkd App: Generate & Display Code → User Copies Code → External System: Validate & Process
  ```

**Important Design Decision**: Manual code validation is intentionally handled on merchant systems external to the Perkd application. This design:
- Maintains security by keeping validation logic on merchant-controlled systems
- Reduces complexity in the Perkd application
- Allows merchants to implement custom validation rules
- Provides flexibility for different merchant system architectures

**Code Display Workflow**:
1. Perkd generates unique redemption code (`code.store` or `code.instore`)
2. User interface displays code with copy functionality
3. User copies code to clipboard
4. User enters code in merchant system (website, POS, etc.)
5. Merchant system validates code and processes redemption
6. Perkd receives redemption confirmation via API

#### Biometric Authentication (Future Implementation)
- **Planned Feature**: Additional security layer for high-value redemptions
- **Implementation Status**: Not currently implemented
- **Intended Use**: Enhanced security for premium offers and high-value transactions

### Redemption Flow Architecture

The redemption system supports multiple authentication methods and channels:

```mermaid
graph TB
    subgraph "🎯 Redemption Initiation"
        R1[Slide to Redeem<br/>Primary Action]
        R2[Quick Redeem<br/>List Action]
        R3[Auto-Apply<br/>Shopping Integration]
        R4[Scheduled Redeem<br/>Future Use]
    end

    subgraph "🔐 Authentication Methods"
        A1[QR Code Scan<br/>Visual Verification]
        A2[Barcode Display<br/>Merchant Scan]
        A3[NFC Touch<br/>Contactless]
        A4[Manual Code<br/>External Validation]
        A5[Biometric Auth<br/>Security Layer]
    end

    subgraph "🏪 Redemption Channels"
        C1[In-Store<br/>Physical Location]
        C2[Online<br/>E-commerce]
        C3[Mobile App<br/>Integrated Shopping]
        C4[Partner Systems<br/>Via Standard Auth]
        C5[Machine/Vending<br/>Automated Systems]
    end

    subgraph "💳 Payment System (Vouchers Only)"
        P1[Payment Method Selection<br/>Voucher Selection]
        P2[Payment Processing<br/>Value Application]
    end

    subgraph "🔗 Partner App Integration"
        D1[Deep Link Generation<br/>Template Substitution]
        D2[Partner App Launch<br/>Auto-Code Population]
    end

    subgraph "✅ Validation & Completion"
        V1[Offer Validation<br/>Rules Engine]
        V2[Inventory Check<br/>Availability]
        V3[Transaction Processing<br/>Payment Integration]
        V4[Confirmation<br/>Receipt & History]
    end

    %% Redemption Flow (Discounts & Tickets)
    R1 --> A1
    R1 --> A2
    R2 --> A3
    R3 --> C3
    R4 --> A1

    %% Authentication to Channels (STORE/ONLINE only)
    A1 --> C1
    A1 --> C4
    A1 --> C5
    A2 --> C1
    A2 --> C4
    A3 --> C1
    A3 --> C4
    A3 --> C5
    A4 --> C2
    A4 --> C4

    %% Voucher Payment Flow (Separate)
    R3 --> P1
    P1 --> P2
    P2 --> V4

    %% Outbound Deep Link Flow (Perkd → Partner Apps)
    R1 --> D1
    R2 --> D1
    D1 --> D2
    D2 --> V4

    %% Channel to Validation
    C1 --> V1
    C2 --> V1
    C3 --> V1
    C4 --> V1
    C5 --> V1

    %% Validation Flow
    V1 --> V2
    V2 --> V3
    V3 --> V4

    %% Styling with darker backgrounds and white text
    classDef initiation fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef auth fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef channels fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef payment fill:#7c3aed,stroke:#8b5cf6,stroke-width:2px,color:#ffffff
    classDef deeplink fill:#f59e0b,stroke:#f97316,stroke-width:2px,color:#ffffff
    classDef validation fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class R1,R2,R3,R4 initiation
    class A1,A2,A3,A4,A5 auth
    class C1,C2,C3,C4,C5 channels
    class P1,P2 payment
    class D1,D2 deeplink
    class V1,V2,V3,V4 validation
```

#### Partner Systems Integration Architecture

Partner systems integration operates through two distinct patterns: inbound authentication and outbound deep link integration:

**Inbound Partner Integration (Authentication Methods):**
- **QR Code Integration**: Partner systems generate QR codes that contain standard Perkd redemption data
- **NFC Integration**: Partner NFC devices communicate using standard Perkd NFC protocols
- **No Custom Logic Required**: Partner systems use the same authentication methods as direct redemptions

**Outbound Deep Link Integration (Perkd → Partner Apps):**
- **Deep Link Generation**: Perkd generates deep links with embedded redemption codes that open partner applications
- **Automatic Code Population**: Redemption codes are automatically populated in partner apps through URL template substitution
- **Seamless User Experience**: Users can redeem offers directly within partner applications without manual code entry
- **Template-Based URLs**: Partner app URLs use Mustache template syntax for dynamic data insertion

**Technical Implementation:**

*Inbound Integration:*
- Partner systems implement standard QR code generation with Perkd-compatible data format
- NFC integration uses existing Perkd NFC protocols and data structures
- All validation and business logic remains within Perkd system boundaries

#### Machine and Vending System Integration

Machine and vending system redemption is implemented as part of the standard in-store redemption channel with specialized location-based validation:

**Integration Architecture:**
- **Machine Identification**: Each machine/vending system has a unique `machineId` identifier
- **Location Mapping**: Machines are mapped to specific places using `_.Place.findByMachineId(machineId)`
- **Authentication Methods**: Supports both QR code scanning and NFC touch authentication
- **Validation Flow**: Standard redemption flow with additional machine-specific context

**Machine Redemption Process:**
1. **Machine Detection**: System identifies machine via `machineId` parameter
2. **Location Validation**: Verifies machine location against offer constraints
3. **Authentication**: User authenticates via QR code scan or NFC touch
4. **Context Integration**: Machine context included in redemption data
5. **Standard Processing**: Follows normal redemption validation and completion flow

**Use Cases:**
- **Vending Machines**: Product purchase with offer discount application
- **Kiosk Systems**: Self-service redemption at retail locations
- **Automated Dispensers**: Ticket or product dispensing with offer validation
- **Smart Lockers**: Package pickup with offer-based access control

**Technical Requirements:**
- Machine systems must implement standard Perkd QR code or NFC protocols
- Machine location must be registered in Perkd place management system
- Redemption context includes machine identification for audit and analytics
- Standard offer validation rules apply (time constraints, usage limits, etc.)

### Sharing and Social Flow

The sharing system enables viral distribution of offers while maintaining security and policy compliance:

#### Sharing Mechanisms
1. **Direct Sharing**: One-to-one sharing via SMS, email, or in-app messaging
2. **Social Broadcasting**: Share to social media platforms with tracking
3. **Group Sharing**: Distribute to multiple recipients simultaneously
4. **Link Generation**: Create shareable links with expiration and usage limits

#### Sharing Policies
- **Transfer Limits**: Configurable limits on sharing frequency and recipients
- **Eligibility Rules**: User status and behavior requirements for sharing
- **Expiration Management**: Shared offers inherit or modify expiration rules
- **Tracking and Analytics**: Complete audit trail of sharing activity

## Data Flow Architecture

The data flow architecture ensures consistent, real-time synchronization between local storage, user interfaces, and backend services while maintaining performance and reliability.

### Data Synchronization Flow

```mermaid
graph TB
    subgraph "📱 Client-Side Data Flow"
        C1[User Actions<br/>UI Interactions]
        C2[Local State<br/>Realm Database]
        C3[Event System<br/>Real-time Updates]
        C4[UI Components<br/>React Native]
    end

    subgraph "🔄 Synchronization Layer"
        S1[Sync Engine<br/>Bidirectional]
        S2[Change Tracking<br/>Delta Updates]
        S3[Conflict Resolution<br/>Merge Strategies]
        S4[Offline Queue<br/>Deferred Operations]
    end

    subgraph "🌐 Backend Integration"
        B1[REST APIs<br/>CRUD Operations]
        B2[WebSocket<br/>Real-time Events]
        B3[Database<br/>Persistent Storage]
        B4[Business Logic<br/>Server Processing]
    end

    subgraph "🔧 Supporting Services"
        SS1[Cache Layer<br/>Performance]
        SS2[Analytics<br/>Usage Tracking]
        SS3[Notifications<br/>Push Delivery]
        SS4[Security<br/>Validation]
    end

    %% Client-Side Flow
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> C1

    %% Synchronization Flow
    C2 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> S4

    %% Backend Integration
    S1 --> B1
    S1 --> B2
    B1 --> B3
    B2 --> B4

    %% Supporting Services
    S1 --> SS1
    B4 --> SS2
    B4 --> SS3
    B1 --> SS4

    %% Styling with darker backgrounds and white text
    classDef client fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef sync fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef backend fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef support fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class C1,C2,C3,C4 client
    class S1,S2,S3,S4 sync
    class B1,B2,B3,B4 backend
    class SS1,SS2,SS3,SS4 support
```

### Data Flow Patterns

#### 1. Offer Discovery Data Flow
1. **Location Update**: GPS coordinates trigger geofencing checks
2. **Context Analysis**: User behavior and preferences analyzed
3. **Offer Matching**: Backend algorithms match relevant offers
4. **Local Sync**: New offers synchronized to local database
5. **UI Update**: Event system notifies UI components of new offers

#### 2. Redemption Data Flow
1. **User Initiation**: User triggers redemption action in UI
2. **Local Update**: Optimistic update to local database
3. **Backend Validation**: Server validates redemption rules and availability
4. **Confirmation**: Success/failure response updates local state
5. **Event Propagation**: UI components receive real-time updates

#### 3. Sharing Data Flow
1. **Share Request**: User initiates sharing with recipient selection
2. **Policy Validation**: System checks sharing policies and limits
3. **Transfer Processing**: Backend creates shared offer instances
4. **Notification Delivery**: Recipients receive share notifications
5. **State Synchronization**: All parties receive updated offer states

## API Endpoints and Services

The Offers Management System exposes a comprehensive REST API that supports all offer lifecycle operations with robust error handling and security measures.

### Core API Endpoints

The offer service layer provides standardized endpoints for all offer operations:

#### Offer Retrieval
```javascript
// Fetch multiple offers by IDs
GET /Offers/app/fetch?ids={offerIds}
```

#### Offer Redemption
```javascript
// Redeem an offer instance
POST /Offers/{id}/app/redeem
Body: { at: timestamp, count: number, options: object }
```

#### Offer Request/Issuance
```javascript
// Request new offer from card master
POST /CardMasters/{id}/app/offer/request
Body: { cardId: string, offerId: string, quantity: number }
```

#### Offer Sharing
```javascript
// Share offer to single recipient
POST /Offers/{id}/app/share
Body: { recipient: object, mode: string, options: object }

// Share offer to multiple recipients
POST /Offers/{id}/app/shareToMany
Body: { recipients: array, mode: string, options: object }

// Cancel pending share
POST /Offers/{id}/app/share/cancel
Body: { sharingId: string }
```

#### Notification Management
```javascript
// Mark offers as notified/viewed
POST /Offers/app/notified
Body: { idList: array, at: timestamp, view: string }
```

### Service Layer Architecture

The service layer abstracts API communication and provides consistent interfaces for offer operations:

````javascript
export default {
    fetch: ({ ids }) => call({ ...API.fetch, qs: { ids } }),

    redeem: (id, at = newDate(), count = 1, options) => call({
        ...API.redeem,
        routeParams: { id },
        body: { at, count, options },
    }),

    request: (id, cardId, offerId, quantity = 1) => call({
        ...API.request,
        routeParams: { id },
        body: { cardId, offerId, quantity },
    }),

    share: ({ id, recipient = {}, mode, options = {} }) => call({
        ...API.share,
        routeParams: { id },
        body: { recipient, mode, options },
    }),
};
````

### API Configuration and Settings

API endpoints are configured through the centralized settings system with comprehensive timeout and error handling options:

**Endpoint Configuration Features:**
- **Timeout Management**: Configurable timeouts for different operation types
- **Error Handling**: Automatic retry logic and user-friendly error messages
- **Security Headers**: Authentication tokens and request signing
- **Rate Limiting**: Built-in protection against excessive API calls

### Error Handling and Resilience

The API layer implements comprehensive error handling strategies:

#### Network Error Handling
- **Automatic Retry**: Exponential backoff for transient failures
- **Offline Queue**: Operations queued when network unavailable
- **Graceful Degradation**: Local-only operations when possible
- **User Feedback**: Clear error messages and recovery suggestions

#### Business Logic Errors
- **Validation Errors**: Input validation with specific error codes
- **Authorization Errors**: Permission and authentication failures
- **Resource Conflicts**: Concurrent modification handling
- **Rate Limiting**: Throttling and quota management

## Business Logic and State Management

The business logic layer orchestrates complex offer operations while maintaining data consistency and enforcing business rules across the entire system.

### State Management Architecture

The offer state management system provides centralized control over offer lifecycle with sophisticated state transitions and validation:

````javascript
export const Offers = {
    stateOf: (offer, now = newDate()) => {
        const { redemption, startTime, endTime, state, when } = offer || {};

        if ([TRANSFERRED, TRANSFERRING, FULLY_REDEEMED, CANCELLED].includes(state)) {
            return state;
        }

        const { redeemed, authorized } = when || {},
            { remain } = redemption || {};

        if (remain !== null && redeemed && remain < 1) return REDEEMED;
        if (authorized) return REDEEMED;
        if (startTime && (new Date(startTime) > now)) return UPCOMING;
        if (endTime && (new Date(endTime) < now)) return EXPIRED;

        return ACTIVE;
    },

    valid: (offer) => {
        const state = Offers.stateOf(offer);
        return [ACTIVE, UPCOMING].includes(state);
    },
};
````

### Business Rules Engine

The system implements a comprehensive business rules engine that governs offer behavior:

#### Redemption Rules
- **Usage Limits**: Single-use, multi-use, and unlimited redemption patterns
- **Time Constraints**: Start/end times, blackout periods, and scheduling rules
- **Location Restrictions**: Geofenced redemption areas and venue-specific offers
- **User Eligibility**: Account status, loyalty tier, and behavioral requirements

#### Sharing Rules
- **Transfer Policies**: Who can share, how many times, and to whom
- **Inheritance Rules**: How shared offers inherit or modify original properties
- **Expiration Handling**: Shared offer expiration relative to original or absolute
- **Tracking Requirements**: Audit trails and analytics for shared offers

#### Validation Logic
- **Real-Time Validation**: Instant verification of offer validity and availability
- **Conflict Resolution**: Handling concurrent redemption attempts
- **Inventory Management**: Stock tracking for limited-quantity offers
- **Fraud Prevention**: Anomaly detection and security measures

### Advanced Business Rules and Logic Patterns

The offers system implements sophisticated business logic that governs complex scenarios and edge cases beyond basic offer management.

#### Offer State Transition Business Rules

**Time-Based State Transitions:**
- **Upcoming to Active**: Offers automatically transition from `UPCOMING` to `ACTIVE` when `startTime` is reached, but only if the card is still valid
- **Active to Expired**: Offers transition to `EXPIRED` when `endTime` passes, regardless of redemption status
- **Redemption State Logic**: Offers with `when.redeemed` or `when.authorized` timestamps transition to `REDEEMED` state
- **Inventory Depletion**: Offers with `redemption.remain < 1` automatically transition to `FULLY_REDEEMED` when redemption limits are exhausted

**Card Lifecycle Integration:**
- **Card Validity Dependency**: Offers are only visible and redeemable when their associated card is active and within its validity period
- **Card Expiration Impact**: When a card expires (`endTime < NOW`), all associated offers become unavailable regardless of their individual expiration dates
- **Card Deletion Cascade**: When a card is deleted, all associated offers are marked with `deletedAt` timestamp for soft deletion
- **Active Card Filtering**: Only offers from active cards are included in unread counts and discovery algorithms

#### Redemption Eligibility and Authorization

**Multi-Level Authorization Checks:**
- **Merchant Authorization**: Redemption requires authorization through `masterIds`, `merchantIds`, or `cardMasterIds` validation
- **Channel Restrictions**: Offers can be restricted to specific redemption channels via `redemption.channels` array (e.g., "perkd", "instore")
- **Authorization Methods**: Different authorization methods (`SCAN`, `NFC`, manual) have different validation requirements
- **Location-Based Validation**: Venue-specific offers require GPS proximity validation within configured range limits

**Inventory and Concurrency Management:**
- **Optimistic Locking**: Local redemption occurs immediately with `localRedeem()` followed by server validation
- **Reservation System**: During checkout, offers are reserved via `Reserved.add()` to prevent double-redemption
- **Recovery Mechanisms**: Failed transactions trigger `recover()` to restore offer availability
- **Housekeeping Logic**: Periodic cleanup checks payment status and removes reservations for completed or cancelled orders

#### Sharing and Transfer Business Logic

**Generation Tracking and Provenance:**
- **Generation Inheritance**: Shared offers track their generation depth via `sharer.generation` to prevent infinite sharing chains
- **Origin Tracking**: Each shared offer maintains `originId` and `sharingId` for complete audit trails
- **Share Mode Enforcement**: Different sharing modes (`transfer`, `invite`) have different inheritance rules and limitations
- **Policy Validation**: Sharing policies from `card.master.sharePolicies` govern who can share to whom

**Share State Management:**
- **Transferring State**: Offers in `TRANSFERRING` state cannot be redeemed until transfer completes or is cancelled
- **Share Cancellation**: Pending shares can be cancelled via `shareCancel()` which reverts the offer to its original state
- **Recipient Validation**: Share recipients must meet eligibility criteria defined in sharing policies
- **Cross-Card Sharing**: Offers can be shared across different card types based on `toCardMasterIds` configuration

#### Payment Integration and Transaction Validation

**Order Processing Integration:**
- **Offer Reservation**: During checkout, eligible offers are reserved via `_.Offer.reserve(ids, masterId, orderId)`
- **Payment Status Monitoring**: System monitors payment intent status (`paid`, `cancelled`) to determine offer fate
- **Transaction Rollback**: Failed payments trigger `_.Offer.recover(ids)` to restore offer availability
- **Discount Application**: Offers automatically apply discounts during checkout based on item matching rules

**Item Matching and Qualification:**
- **Product Qualification**: Offers validate against specific items via `Offers.items.match(itemsToRedeem, offerItems)`
- **Order Context**: Offers can be tied to specific orders via `orderId` for targeted redemption
- **Quantity Validation**: Redemption quantity is validated against offer limits and remaining availability
- **Price Threshold**: Some offers require minimum purchase amounts for eligibility

#### Fraud Prevention and Security Measures

**Velocity and Pattern Analysis:**
- **Redemption Rate Limiting**: System tracks redemption frequency to detect unusual patterns
- **Device Correlation**: Cross-device activity analysis helps identify potential fraud
- **Scan Validation**: Barcode scanning requires multiple successful reads for high-value offers
- **Location Verification**: GPS validation ensures redemption occurs at authorized venues

**Business Rule Enforcement:**
- **Time Window Validation**: Strict enforcement of offer validity windows with server-side verification
- **Usage Limit Enforcement**: Redemption limits are enforced both locally and server-side
- **Authorization Validation**: Multiple layers of authorization checks prevent unauthorized redemption
- **Audit Trail Maintenance**: Complete tracking of all offer state changes and redemption attempts

#### Advanced Integration Patterns

**Card Master Policy Integration:**
- **Bag Policy Offers**: Special offers defined in `cardMaster.bagPolicy` for shopping cart integration
- **Fulfillment Policy**: Offers respect fulfillment policies for delivery, pickup, and in-store redemption
- **Channel-Specific Rules**: Different business rules apply based on redemption channel (mobile app, in-store, online)
- **Merchant-Specific Constraints**: Individual merchants can define custom redemption rules and restrictions

**Cross-System Validation:**
- **Real-Time Inventory**: Integration with inventory systems for stock-dependent offers
- **Payment System Coordination**: Coordination with payment processors for transaction validation
- **Location Service Integration**: GPS and geofencing validation for location-based offers
- **User Account Status**: Integration with user account status and loyalty tier validation

#### Offer Lifecycle Edge Cases and Special Scenarios

**Purging and Cleanup Logic:**
- **Automatic Purging**: Offers with `purgeTime` set are automatically removed from local storage after the specified date
- **Soft Deletion**: Offers are soft-deleted via `markDelete()` which sets `deletedAt` timestamp rather than hard deletion
- **Orphaned Offer Handling**: Offers without valid associated cards are filtered out during discovery and listing
- **Expired Offer Retention**: Expired offers are retained for historical purposes and analytics but excluded from active operations

**Multi-Use and Redemption Limit Logic:**
- **Null Remain Handling**: When `redemption.remain` is `null`, the offer has unlimited redemptions
- **Zero Remain Logic**: When `redemption.remain` reaches 0, the offer transitions to `FULLY_REDEEMED` state
- **Multiple Flag**: The `redemption.multiple` flag determines if an offer can be redeemed more than once by the same user
- **Limit Enforcement**: The `redemption.limit` defines the maximum number of redemptions across all users

**Time-Sensitive Business Rules:**
- **Grace Period Handling**: Offers may have implicit grace periods where they remain redeemable slightly past expiration
- **Timezone Considerations**: All time-based validations use server timezone to ensure consistency across regions
- **Blackout Periods**: Some offers may have blackout periods where redemption is temporarily disabled
- **Scheduled Activation**: Offers can be pre-created with future `startTime` for scheduled campaigns

**Error Recovery and Resilience:**
- **Network Failure Recovery**: Failed redemption attempts are queued via `_.Action.defer()` for retry when connectivity returns
- **State Inconsistency Resolution**: Local and server state mismatches are resolved through periodic synchronization
- **Partial Failure Handling**: If part of a multi-offer redemption fails, the system gracefully handles partial success
- **Rollback Mechanisms**: Failed transactions trigger automatic rollback of local state changes

#### Business Logic Integration Points

**Shopping Cart and Checkout Integration:**
- **Automatic Discount Application**: Eligible offers automatically apply discounts during checkout without user intervention
- **Discount Stacking Rules**: Complex rules govern how multiple offers can be combined in a single transaction
- **Item-Level Validation**: Offers validate against specific cart items based on product categories and qualifiers
- **Real-Time Price Updates**: Cart totals update in real-time as offers are applied or removed

**Loyalty Program Integration:**
- **Tier-Based Eligibility**: Offer availability may depend on user's loyalty tier or account status
- **Points Integration**: Some offers may require loyalty points in addition to or instead of monetary payment
- **Behavior-Based Targeting**: Offers are targeted based on user purchase history and behavior patterns
- **Cross-Program Benefits**: Offers may provide benefits across multiple loyalty programs or merchant partnerships

**Notification and Engagement Rules:**
- **Notification Timing**: Offers trigger notifications based on sophisticated timing algorithms considering user behavior
- **Engagement Tracking**: The system tracks when offers are viewed, shared, and redeemed for analytics
- **Reminder Logic**: Expiring offers trigger reminders based on user preferences and offer value
- **Contextual Triggers**: Location-based and time-based triggers activate relevant offer notifications

**Analytics and Business Intelligence:**
- **Redemption Analytics**: Comprehensive tracking of redemption patterns, success rates, and user engagement
- **Performance Metrics**: Offers track conversion rates, sharing velocity, and revenue impact
- **A/B Testing Support**: The system supports offer variations for testing different promotional strategies
- **Fraud Detection**: Machine learning algorithms analyze redemption patterns to detect potential fraud

These business rules ensure the offers system operates reliably across complex real-world scenarios while maintaining data integrity, user experience quality, and business objective alignment. The sophisticated rule engine handles edge cases gracefully while providing the flexibility needed for diverse promotional strategies and merchant requirements.

### Controller Architecture

The Offer Controller serves as the primary orchestrator for offer-related operations:

````javascript
async redeem(offer) {
    const { card } = this,
        { options, redemption } = offer,
        { actions, buttonLink, appOnly } = options || {},
        authorize = redemption?.authorize || options?.authorize;

    if (authorize === SCAN) return this.byScan(offer);
    if (authorize === NFC) return this.byNfc(offer);

    // Handle action-based redemption
    if (acts) {
        offer.update({ items });
        const rendered = renderObject(acts, { card, offer });
        await this.doAction(rendered);
    }
}
````

#### Controller Responsibilities
- **Operation Coordination**: Orchestrates complex multi-step operations
- **State Validation**: Ensures operations comply with business rules
- **Event Management**: Emits events for UI updates and analytics
- **Error Handling**: Provides consistent error handling across operations
- **Integration Management**: Coordinates with card, payment, and location services

#### Core Controller Methods

The Offer Controller provides a comprehensive API for offer management operations:

**Data Management Methods:**
- **refresh(data, init)**: Updates the offer list and grouped data with real-time synchronization
- **getGrouped()**: Organizes offers by status and priority for optimal user experience
- **updateTopbar(props)**: Manages navigation bar state and pagination information

**View Presentation Methods:**
- **view(callbacks, componentId, ...)**: Renders the offer view with appropriate navigation context
- **openList()**: Opens the offer list view with filtering and sorting options
- **openDetail(offer)**: Displays detailed offer information with rich media and actions

**Redemption Management Methods:**
- **redeem(offer)**: Initiates the redemption flow for an offer with validation
- **openRedeem(offer, options)**: Opens the redemption confirmation screen with context
- **byScan(offer)**: Handles QR/barcode scanning redemption flow
- **byNfc(offer)**: Manages NFC-based redemption authentication

**Sharing and Social Methods:**
- **share(offer)**: Starts the sharing process for an offer with policy validation
- **cancelShare(offer, sharingId)**: Cancels pending share operations
- **notified(offers, timestamp)**: Marks offers as viewed for analytics tracking

#### Navigation Control Patterns

The controller implements sophisticated navigation patterns optimized for mobile user experience:

**Navigation Types:**
- **List to Detail**: Seamless navigation between offer list and individual offer details
- **Modal Presentations**: Shows redemption confirmations and sharing interfaces as modals
- **Redemption Flows**: Manages complex multi-step redemption processes
- **Return Navigation**: Handles back navigation after completion with state preservation

**Navigation State Management:**
- **Deep Link Support**: Direct navigation to specific offers via URL schemes
- **State Preservation**: Maintains navigation state across app lifecycle events
- **Context Awareness**: Navigation adapts based on user context and offer state
- **Performance Optimization**: Lazy loading of navigation targets for improved performance

## Integration Architecture

The Offers Management System integrates seamlessly with multiple subsystems within the Perkd ecosystem, providing cohesive user experiences and maintaining data consistency across all touchpoints.

### Card Management Integration

The offers system is deeply integrated with the card management infrastructure:

#### Card-Offer Association
- **Automatic Discovery**: Offers automatically surface when users view associated cards
- **Lifecycle Synchronization**: Card state changes affect offer availability and presentation
- **Cross-Reference Navigation**: Seamless navigation between cards and their associated offers
- **Unified Analytics**: Combined tracking of card and offer engagement metrics

#### Data Sharing Patterns
- **Shared Models**: Card and offer models share common data structures and validation
- **Event Coordination**: Card events trigger offer updates and vice versa
- **State Consistency**: Coordinated state management ensures data integrity
- **Performance Optimization**: Shared caching strategies reduce redundant data loading

### Payment System Integration

Offers integrate with payment processing to enable automatic discount application and transaction tracking:

#### Transaction Processing
- **Automatic Application**: Eligible offers automatically apply during checkout
- **Real-Time Validation**: Payment flow validates offer eligibility and availability
- **Transaction Linking**: Offers track associated payment transactions
- **Refund Handling**: Offer redemptions properly handle payment refunds and reversals

#### Shopping Cart Integration
- **Cart-Level Discounts**: Offers can apply to entire shopping cart or specific items
- **Stacking Rules**: Complex rules govern how multiple offers interact
- **Preview Functionality**: Users see discount previews before completing purchase
- **Inventory Coordination**: Offer availability syncs with product inventory

### Location Services Integration

Location-based functionality enables contextual offer discovery and geofenced redemption:

#### Geofencing and Proximity
- **Automatic Triggers**: Location changes trigger relevant offer notifications
- **Venue-Specific Offers**: Offers tied to specific merchant locations
- **Proximity Scoring**: Distance-based offer relevance and sorting
- **Privacy Controls**: User-controlled location sharing and tracking preferences

#### Place and Venue Integration
- **Merchant Mapping**: Offers linked to specific merchant locations and venues
- **Navigation Integration**: Direct navigation to offer redemption locations
- **Hours and Availability**: Offer availability respects merchant operating hours
- **Multi-Location Support**: Chain merchants with location-specific offer variations

### Notification and Engagement Integration

The offers system leverages the notification infrastructure for user engagement:

#### Push Notification Integration
- **Contextual Triggers**: Location, time, and behavior-based offer notifications
- **Personalization**: Machine learning-driven notification targeting
- **Delivery Optimization**: Optimal timing and frequency for maximum engagement
- **Deep Link Support**: Notifications deep link directly to specific offers

#### In-App Messaging
- **Contextual Messages**: In-app messages promote relevant offers
- **Rich Media Support**: Images, videos, and interactive content in offer messages
- **Action Integration**: Direct redemption and sharing actions from messages
- **Analytics Tracking**: Comprehensive tracking of message engagement and conversion

### Reminder System Integration

The offers system integrates with the reminder infrastructure to provide proactive user engagement:

#### Expiration Reminders
- **Smart Timing**: Expiring offers trigger reminders based on user preferences and behavior patterns
- **Customizable Rules**: Custom reminder rules can be applied to different offer types
- **Multi-Channel Delivery**: Reminders delivered via push notifications, in-app messages, and email
- **User Control**: Users can customize reminder frequency and timing preferences

#### State-Based Reminders
- **Offer Lifecycle Tracking**: Reminder system tracks offer states and timelines automatically
- **Contextual Triggers**: Location-based reminders when users are near redemption venues
- **Behavioral Triggers**: Reminders based on user shopping patterns and preferences
- **Intelligent Scheduling**: Machine learning optimizes reminder timing for maximum effectiveness

## Security and Validation

The Offers Management System implements comprehensive security measures to protect against fraud, ensure data integrity, and maintain user privacy throughout the offer lifecycle.

### Authentication and Authorization

#### Multi-Layer Security
- **User Authentication**: Biometric and PIN-based user verification
- **Device Validation**: Device fingerprinting and trusted device management
- **Session Management**: Secure session handling with automatic timeout
- **API Authentication**: JWT tokens with automatic refresh and validation

#### Permission Management
- **Role-Based Access**: Different permission levels for users, merchants, and administrators
- **Operation-Specific Permissions**: Granular permissions for different offer operations
- **Dynamic Authorization**: Real-time permission validation based on context
- **Audit Logging**: Comprehensive logging of all security-related events

### Data Protection and Privacy

#### Encryption and Secure Storage
- **Data at Rest**: AES encryption for sensitive offer data in local storage
- **Data in Transit**: TLS encryption for all API communications
- **Key Management**: Secure key rotation and management practices
- **Selective Encryption**: Different encryption levels based on data sensitivity

#### Privacy Compliance
- **Data Minimization**: Collection and storage of only necessary data
- **User Consent**: Clear consent mechanisms for data collection and sharing
- **Right to Deletion**: User ability to delete offer history and associated data
- **Cross-Border Compliance**: GDPR and regional privacy regulation compliance

### Fraud Prevention and Validation

#### Real-Time Validation
- **Offer Authenticity**: Cryptographic validation of offer integrity
- **Usage Pattern Analysis**: Machine learning-based fraud detection
- **Velocity Checks**: Rate limiting and unusual activity detection
- **Device Correlation**: Cross-device activity analysis for fraud prevention

#### Business Rule Enforcement
- **Redemption Limits**: Strict enforcement of usage limits and restrictions
- **Time Window Validation**: Precise validation of time-based offer constraints
- **Location Verification**: GPS and network-based location validation
- **Eligibility Verification**: Real-time validation of user eligibility criteria

#### Validation Patterns and Business Constraints

**Scan Validation and Security:**
- **Multi-Scan Verification**: High-value offers require multiple successful barcode scans for validation (configurable via `validation.times`)
- **Format Validation**: Only specific barcode formats are accepted based on `validation.types` configuration
- **Temporal Validation**: Scan results must be consistent across multiple attempts within a time window
- **Device Fingerprinting**: Scanning device characteristics are validated to prevent replay attacks

**Authorization Method Business Rules:**
- **NFC Proximity**: NFC-based redemption requires physical proximity and secure element validation
- **QR Code Security**: QR codes include cryptographic signatures and expiration timestamps
- **Manual Code Entry**: Manual codes have additional validation layers including check digits and format verification
- **Biometric Authentication**: High-value redemptions may require biometric confirmation

**Channel-Specific Constraints:**
- **App-Only Offers**: Some offers are restricted to mobile app redemption via `options.appOnly` flag
- **In-Store Only**: Physical location validation required for in-store redemption channels
- **Online Restrictions**: Web-based redemption may have different validation requirements
- **Partner Channel Rules**: Third-party redemption channels have specific authorization requirements

**Item Matching and Qualification Logic:**
- **Product Category Validation**: Offers validate against specific product categories using sophisticated matching algorithms
- **SKU-Level Matching**: Precise product matching down to specific SKUs and variants
- **Quantity Thresholds**: Minimum and maximum quantity requirements for offer eligibility
- **Price-Based Qualification**: Offers may require minimum purchase amounts or price thresholds

**Temporal Business Constraints:**
- **Redemption Windows**: Offers may have specific time windows when redemption is allowed (e.g., lunch hours only)
- **Cooldown Periods**: Some offers enforce cooldown periods between redemptions by the same user
- **Seasonal Restrictions**: Holiday or seasonal offers with specific date range constraints
- **Recurring Availability**: Weekly or monthly recurring offers with complex scheduling rules

**Geographic and Location Constraints:**
- **Geofencing Validation**: Precise GPS coordinate validation within merchant-defined boundaries
- **Venue-Specific Rules**: Different rules for different store locations of the same merchant
- **Regional Restrictions**: Country or region-specific offer availability and redemption rules
- **Distance-Based Eligibility**: Offers that require users to be within a certain distance of redemption location

## Performance Optimization

The Offers Management System employs sophisticated performance optimization strategies to ensure responsive user experiences and efficient resource utilization.

### Data Access Optimization

#### Multi-Level Caching Strategy
- **Memory Cache**: Frequently accessed offers cached in application memory
- **Database Optimization**: Indexed queries and optimized Realm database schemas
- **Network Cache**: HTTP response caching with intelligent invalidation
- **Predictive Loading**: Machine learning-driven prefetching of likely-needed offers

#### Query Optimization
- **Efficient Filtering**: Optimized database queries with proper indexing
- **Batch Operations**: Grouped operations to reduce database overhead
- **Lazy Loading**: On-demand loading of offer details and media
- **Pagination**: Efficient pagination for large offer collections

### UI Performance

#### Rendering Optimization
- **Virtualized Lists**: Long offer lists use virtualization for memory efficiency with React Native's VirtualizedList
- **Image Optimization**: Offer images are optimized for display size with lazy loading and WebP format support
- **Component Memoization**: React component optimization to prevent unnecessary re-renders using React.memo and useMemo
- **Animation Performance**: Hardware-accelerated animations using native drivers for smooth 60fps performance
- **Deferred Rendering**: Complex UI elements are rendered only when visible to improve initial load times

#### State Management Performance
- **Selective Updates**: Granular state updates to minimize re-rendering
- **Event Debouncing**: Throttled event handling to prevent performance degradation
- **Memory Management**: Efficient cleanup of unused offer data and components
- **Background Processing**: CPU-intensive operations moved to background threads

### Network Optimization

#### Request Optimization
- **Request Batching**: Multiple API requests combined for efficiency
- **Compression**: Gzip compression for reduced bandwidth usage
- **Connection Pooling**: Efficient HTTP connection reuse
- **Offline Capabilities**: Robust offline functionality with sync queuing

#### Synchronization Efficiency
- **Delta Sync**: Only changed data synchronized between client and server
- **Conflict Resolution**: Efficient handling of concurrent modifications
- **Priority Queuing**: Critical operations prioritized in sync queue
- **Bandwidth Adaptation**: Sync behavior adapts to network conditions

## Error Handling

The Offers Management System implements comprehensive error handling strategies that ensure robust operation and excellent user experience across all failure scenarios.

### Error Classification and Handling

#### Network and Connectivity Errors
- **Automatic Retry**: Exponential backoff retry logic for transient failures
- **Offline Fallback**: Graceful degradation to cached data when network unavailable
- **Connection Quality Adaptation**: Behavior modification based on connection quality
- **User Communication**: Clear messaging about network issues and recovery options

#### Business Logic Errors
- **Validation Errors**: Comprehensive input validation with user-friendly error messages
- **State Conflicts**: Handling of concurrent modifications and state conflicts
- **Resource Limitations**: Graceful handling of quota and rate limit violations
- **Authorization Failures**: Clear communication of permission and access issues

#### System and Application Errors
- **Exception Handling**: Comprehensive try-catch blocks with proper error propagation
- **Graceful Degradation**: System continues functioning with reduced capabilities
- **Error Recovery**: Automatic recovery mechanisms for common error scenarios
- **Crash Prevention**: Defensive programming to prevent application crashes

### User Experience During Errors

#### Error Communication
- **Contextual Messages**: Error messages tailored to user context and action
- **Recovery Guidance**: Clear instructions for resolving error conditions
- **Progressive Disclosure**: Detailed error information available on demand
- **Multilingual Support**: Error messages localized for all supported languages

#### Fallback Mechanisms
- **Cached Data**: Display of cached offer data when real-time data unavailable
- **Alternative Flows**: Alternative user paths when primary flows fail
- **Partial Functionality**: Core features remain available during partial system failures
- **Graceful Retry**: User-initiated retry mechanisms with progress indication

## Implementation Patterns

The Offers Management System follows established architectural patterns that promote maintainability, testability, and scalability while ensuring consistent development practices.

### Component Architecture Patterns

#### Presentation-Container Pattern
- **Container Components**: Handle data fetching, state management, and business logic
- **Presentation Components**: Focus solely on UI rendering and user interaction
- **Props-Based Communication**: Clean interfaces between containers and presentations
- **Reusable Components**: Shared components across different offer contexts

#### Controller Pattern
- **Centralized Logic**: Business logic centralized in controller classes
- **Service Coordination**: Controllers orchestrate interactions between services
- **State Management**: Controllers manage complex state transitions
- **Event Integration**: Controllers emit and consume events for loose coupling

### Data Flow Patterns

#### Unidirectional Data Flow
1. **User Actions**: UI interactions trigger controller methods
2. **Business Logic**: Controllers process actions and update models
3. **Data Persistence**: Models persist changes to local database
4. **Event Emission**: Changes trigger events for UI updates
5. **UI Updates**: Components receive new data through props and re-render

#### Event-Driven Architecture
- **Loose Coupling**: Components communicate through events rather than direct references
- **Real-Time Updates**: Events enable real-time UI updates across the application
- **Cross-Feature Communication**: Events facilitate communication between different features
- **Analytics Integration**: Events provide hooks for analytics and tracking

### Service Layer Patterns

#### Service Abstraction
- **Consistent Interfaces**: Standardized interfaces across different service types
- **Error Handling**: Centralized error handling and transformation
- **Caching Integration**: Built-in caching strategies for performance
- **Testing Support**: Mock-friendly interfaces for unit testing

#### Integration Patterns
- **Adapter Pattern**: Adapters for different external service interfaces
- **Circuit Breaker**: Protection against cascading failures in external services
- **Retry Logic**: Configurable retry strategies for different operation types
- **Fallback Mechanisms**: Graceful degradation when external services unavailable

### Development Guidelines and Best Practices

When working with the Offers Management System, developers should follow these established patterns and best practices to ensure consistency, maintainability, and optimal performance.

#### Architecture Patterns

**Widget Extension Patterns:**
- **Extend Base Classes**: Extend the base Offer Widget for specialized functionality rather than creating standalone components
- **Controller Delegation**: Delegate business logic to the Offer Controller rather than embedding it in UI components
- **Model Isolation**: Keep data manipulation within model methods to maintain data integrity
- **Event-Driven Updates**: Use the event system for cross-component communication to maintain loose coupling

**Component Design Patterns:**
- **Presentation-Container Separation**: Maintain clear separation between presentation and business logic
- **Props-Based Communication**: Use props for data flow and callbacks for action handling
- **Reusable Components**: Design components for reuse across different offer contexts
- **State Management**: Minimize component state and leverage centralized state management

#### Implementation Best Practices

**Offer Handling:**
- Use the `Offers` utility methods for consistent offer state management and validation
- Implement proper cleanup in widget lifecycle methods to prevent memory leaks
- Follow established redemption flow patterns when implementing new redemption methods
- Support all offer statuses in custom implementations to ensure comprehensive coverage

**Performance Considerations:**
- Implement virtualized lists for large offer collections to maintain smooth scrolling
- Use lazy loading for offer images and detailed content to improve initial load times
- Cache frequently accessed offer data to reduce database queries
- Implement proper error boundaries to prevent offer-related errors from crashing the app

**Integration Guidelines:**
- Use the share controller for all sharing operations to ensure policy compliance
- Integrate with the card system through established widget patterns
- Leverage the notification system for offer-related user engagement
- Follow security best practices for offer validation and redemption

**Testing Strategies:**
- Test offer state transitions thoroughly to ensure business rule compliance
- Mock external services for reliable unit testing
- Implement integration tests for complex offer flows
- Test performance with large datasets to ensure scalability

#### Code Organization

**File Structure:**
- Place offer-specific components in `src/components/Offer/`
- Organize offer containers in `src/containers/Offer/`
- Keep offer models in `src/lib/models/Offer/`
- Maintain offer utilities in `src/lib/offers.js`

**Naming Conventions:**
- Use descriptive names that reflect offer functionality
- Follow established naming patterns for consistency
- Use TypeScript interfaces for complex offer data structures
- Document complex business logic with clear comments

This comprehensive architecture provides a robust, scalable foundation for offer management while maintaining the flexibility needed for future enhancements and evolving business requirements. The system's modular design, comprehensive error handling, and performance optimizations ensure reliable operation at scale while delivering exceptional user experiences.
